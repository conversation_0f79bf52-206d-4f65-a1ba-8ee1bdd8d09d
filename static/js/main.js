// 现代化UI交互系统 - 增强版本

$(document).ready(function() {
    // 初始化所有功能
    initializeModals();
    initializeSearch();
    initializeFileUpload();
    initializeFormValidation();
    initializeNotifications();
    initializeAnimations();
    initializeAccessibility();
    initializeMicroInteractions();
    initializeScrollEffects();
    initializeParallax();
    initializeProgressBars();
    initializeTooltips();

    // 自动隐藏通知消息
    setTimeout(function() {
        hideNotifications();
    }, 5000);

    // 页面加载完成动画
    $('body').addClass('page-loaded');
});

// 模态框功能
function initializeModals() {
    // 处理模态框触发器
    document.querySelectorAll('[data-modal-target]').forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-target');
            showModal(modalId);
        });
    });

    // 处理模态框关闭
    document.querySelectorAll('[data-modal-close]').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) hideModal(modal.id);
        });
    });

    // 点击背景关闭模态框
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-backdrop')) {
            const modal = e.target.querySelector('.modal');
            if (modal) hideModal(modal.id);
        }
    });
}

// 搜索功能
function initializeSearch() {
    const searchInput = $('input[name="search"]');
    const searchForm = searchInput.closest('form');
    let searchTimeout;

    // 实时搜索（可选）
    searchInput.on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                performSearch(query);
            }, 500);
        }
    });

    // 清除搜索
    $('.search-clear').on('click', function(e) {
        e.preventDefault();
        searchInput.val('');
        searchForm.submit();
    });
}

// 执行搜索
function performSearch(query) {
    // 这里可以实现AJAX搜索
    console.log('Searching for:', query);
}

// 文件上传功能
function initializeFileUpload() {
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        const fileInput = $(this).find('input[type="file"]');
        
        if (files.length > 0) {
            fileInput[0].files = files;
            handleFileSelection(fileInput[0]);
        }
    });

    $('input[type="file"]').on('change', function() {
        handleFileSelection(this);
    });
}

// 处理文件选择
function handleFileSelection(input) {
    const file = input.files[0];
    if (!file) return;

    const maxSize = 16 * 1024 * 1024; // 16MB
    if (file.size > maxSize) {
        alert('文件大小不能超过16MB');
        input.value = '';
        return;
    }

    // 显示文件信息
    const fileInfo = $(input).siblings('.file-info');
    if (fileInfo.length) {
        fileInfo.html(`
            <div class="alert alert-info">
                <i class="fas fa-file me-2"></i>
                已选择文件: ${file.name} (${formatFileSize(file.size)})
            </div>
        `);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 表单验证
function initializeFormValidation() {
    // 邮箱验证
    $('input[type="email"]').on('blur', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '请输入有效的邮箱地址');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });

    // URL验证
    $('input[type="url"]').on('blur', function() {
        const url = $(this).val();
        const urlRegex = /^https?:\/\/.+/;
        
        if (url && !urlRegex.test(url)) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '请输入有效的URL（以http://或https://开头）');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });

    // 必填字段验证
    $('input[required], textarea[required], select[required]').on('blur', function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '此字段为必填项');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });
}

// 显示字段错误
function showFieldError(field, message) {
    hideFieldError(field);
    field.after(`<div class="invalid-feedback">${message}</div>`);
}

// 隐藏字段错误
function hideFieldError(field) {
    field.siblings('.invalid-feedback').remove();
}

// AJAX请求辅助函数
function makeAjaxRequest(url, method, data, successCallback, errorCallback) {
    const csrfToken = $('meta[name="csrf-token"]').attr('content');
    
    $.ajax({
        url: url,
        method: method,
        data: data,
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (successCallback) successCallback(response);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            if (errorCallback) {
                errorCallback(xhr, status, error);
            } else {
                alert('请求失败，请稍后重试');
            }
        }
    });
}

// 显示加载状态
function showLoading(element) {
    const originalText = element.html();
    element.data('original-text', originalText);
    element.html('<span class="loading"></span> 处理中...');
    element.prop('disabled', true);
}

// 隐藏加载状态
function hideLoading(element) {
    const originalText = element.data('original-text');
    element.html(originalText);
    element.prop('disabled', false);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('已复制到剪贴板', 'success');
    }).catch(function(err) {
        console.error('复制失败:', err);
        showToast('复制失败', 'error');
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 创建toast容器（如果不存在）
    if (!$('.toast-container').length) {
        $('body').append('<div class="toast-container position-fixed bottom-0 end-0 p-3"></div>');
    }
    
    const $toast = $(toastHtml);
    $('.toast-container').append($toast);
    
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();
    
    // 自动移除
    $toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== 微交互和动画系统 =====

// 初始化微交互
function initializeMicroInteractions() {
    // 按钮点击波纹效果
    initializeRippleEffect();

    // 卡片悬停效果
    initializeCardHoverEffects();

    // 输入框焦点效果
    initializeInputFocusEffects();

    // 加载状态动画
    initializeLoadingStates();

    // 数字计数动画
    initializeCounterAnimations();
}

// 波纹效果
function initializeRippleEffect() {
    $('.btn, .card-hover, .nav-link').on('click', function(e) {
        const button = $(this);
        const ripple = $('<span class="ripple"></span>');

        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.css({
            width: size,
            height: size,
            left: x,
            top: y
        });

        button.append(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
}

// 卡片悬停效果
function initializeCardHoverEffects() {
    $('.card-hover').hover(
        function() {
            $(this).addClass('hover-active');
        },
        function() {
            $(this).removeClass('hover-active');
        }
    );

    // 3D倾斜效果
    $('.project-card').on('mousemove', function(e) {
        const card = $(this);
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;

        card.css('transform', `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`);
    });

    $('.project-card').on('mouseleave', function() {
        $(this).css('transform', 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)');
    });
}

// 输入框焦点效果
function initializeInputFocusEffects() {
    $('.form-input').on('focus', function() {
        $(this).parent().addClass('input-focused');
    });

    $('.form-input').on('blur', function() {
        $(this).parent().removeClass('input-focused');
    });

    // 浮动标签效果
    $('.form-input').on('input', function() {
        const input = $(this);
        const label = input.siblings('.form-label');

        if (input.val()) {
            label.addClass('label-floating');
        } else {
            label.removeClass('label-floating');
        }
    });
}

// 加载状态动画
function initializeLoadingStates() {
    // 表单提交加载状态
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        submitBtn.html(`
            <div class="loading-spinner mr-2"></div>
            <span>处理中...</span>
        `).prop('disabled', true);

        // 保存原始文本以便恢复
        submitBtn.data('original-text', originalText);
    });

    // 链接点击加载状态
    $('a[href]:not([href^="#"]):not([target="_blank"])').on('click', function() {
        const link = $(this);
        if (!link.hasClass('no-loading')) {
            showPageLoading();
        }
    });
}

// 数字计数动画
function initializeCounterAnimations() {
    const counters = $('.stats-value');

    const animateCounter = (counter) => {
        const target = parseInt(counter.text().replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.text(Math.floor(current));
        }, 16);
    };

    // 使用Intersection Observer触发动画
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter($(entry.target));
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.each(function() {
            observer.observe(this);
        });
    }
}

// 滚动效果
function initializeScrollEffects() {
    // 滚动显示动画
    const animateOnScroll = () => {
        $('.animate-on-scroll').each(function() {
            const element = $(this);
            const elementTop = element.offset().top;
            const elementBottom = elementTop + element.outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                element.addClass('animate-visible');
            }
        });
    };

    $(window).on('scroll', throttle(animateOnScroll, 100));
    animateOnScroll(); // 初始检查

    // 滚动进度条
    const updateScrollProgress = () => {
        const scrollTop = $(window).scrollTop();
        const docHeight = $(document).height() - $(window).height();
        const scrollPercent = (scrollTop / docHeight) * 100;

        $('.scroll-progress').css('width', scrollPercent + '%');
    };

    $(window).on('scroll', throttle(updateScrollProgress, 50));
}

// 视差效果
function initializeParallax() {
    $(window).on('scroll', throttle(() => {
        const scrolled = $(window).scrollTop();

        $('.parallax-bg').each(function() {
            const rate = scrolled * -0.5;
            $(this).css('transform', `translateY(${rate}px)`);
        });

        $('.parallax-element').each(function() {
            const rate = scrolled * -0.3;
            $(this).css('transform', `translateY(${rate}px)`);
        });
    }, 16));
}

// 进度条动画
function initializeProgressBars() {
    $('.progress-bar').each(function() {
        const progressBar = $(this);
        const targetWidth = progressBar.data('progress') || 0;

        // 使用Intersection Observer触发动画
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        progressBar.find('.progress-fill').animate({
                            width: targetWidth + '%'
                        }, 1500, 'easeOutCubic');
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(this);
        }
    });
}

// 工具提示
function initializeTooltips() {
    // 简单的工具提示实现
    $('[data-tooltip]').hover(
        function() {
            const tooltip = $(this).data('tooltip');
            const tooltipElement = $(`<div class="tooltip-popup">${tooltip}</div>`);

            $('body').append(tooltipElement);

            const rect = this.getBoundingClientRect();
            tooltipElement.css({
                top: rect.top - tooltipElement.outerHeight() - 10,
                left: rect.left + (rect.width / 2) - (tooltipElement.outerWidth() / 2)
            });

            tooltipElement.fadeIn(200);
        },
        function() {
            $('.tooltip-popup').fadeOut(200, function() {
                $(this).remove();
            });
        }
    );
}

// 页面加载动画
function showPageLoading() {
    if (!$('.page-loading').length) {
        const loadingHtml = `
            <div class="page-loading">
                <div class="loading-spinner-lg"></div>
                <p class="mt-4 text-gray-600">页面加载中...</p>
            </div>
        `;
        $('body').append(loadingHtml);
    }
    $('.page-loading').fadeIn(300);
}

function hidePageLoading() {
    $('.page-loading').fadeOut(300, function() {
        $(this).remove();
    });
}

// 通知系统增强
function initializeNotifications() {
    // 自动隐藏通知
    $('.notification').each(function() {
        const notification = $(this);
        setTimeout(() => {
            notification.addClass('notification-hide');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    });
}

function hideNotifications() {
    $('.notification').addClass('notification-hide');
    setTimeout(() => {
        $('.notification').remove();
    }, 300);
}

// 动画系统
function initializeAnimations() {
    // 为元素添加进入动画
    $('.animate-fade-in, .animate-slide-up, .animate-slide-down, .animate-scale-in, .animate-bounce-in').each(function() {
        $(this).addClass('animate-on-scroll');
    });

    // 交错动画
    $('.stagger-animation').children().each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
}

// 无障碍功能
function initializeAccessibility() {
    // 键盘导航
    $(document).on('keydown', function(e) {
        // ESC键关闭模态框
        if (e.key === 'Escape') {
            $('.modal-container:visible').each(function() {
                hideModal(this.id);
            });
        }

        // Tab键焦点管理
        if (e.key === 'Tab') {
            const focusableElements = $('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            const firstElement = focusableElements.first();
            const lastElement = focusableElements.last();

            if (e.shiftKey && document.activeElement === firstElement[0]) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement[0]) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    });

    // 焦点指示器
    $('button, a, input, select, textarea').on('focus', function() {
        $(this).addClass('focus-visible');
    }).on('blur', function() {
        $(this).removeClass('focus-visible');
    });
}

// 模态框增强功能
function showModal(modalId) {
    const modal = $('#' + modalId);
    if (modal.length) {
        modal.removeClass('hidden').addClass('animate-scale-in');
        $('body').addClass('modal-open');

        // 焦点管理
        const firstFocusable = modal.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])').first();
        firstFocusable.focus();

        // 背景点击关闭
        modal.find('.modal-backdrop').on('click', function() {
            hideModal(modalId);
        });
    }
}

function hideModal(modalId) {
    const modal = $('#' + modalId);
    if (modal.length) {
        modal.removeClass('animate-scale-in');
        setTimeout(() => {
            modal.addClass('hidden');
            $('body').removeClass('modal-open');
        }, 300);
    }
}

// 平滑滚动
function smoothScrollTo(target, duration = 800) {
    const targetElement = $(target);
    if (targetElement.length) {
        $('html, body').animate({
            scrollTop: targetElement.offset().top - 100
        }, duration, 'easeInOutCubic');
    }
}

// 自定义缓动函数
$.easing.easeInOutCubic = function(x, t, b, c, d) {
    if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
    return c / 2 * ((t -= 2) * t * t + 2) + b;
};

$.easing.easeOutCubic = function(x, t, b, c, d) {
    return c * ((t = t / d - 1) * t * t + 1) + b;
};
