# 组件使用指南

本文档提供了项目研究报告平台中所有UI组件的使用指南和最佳实践。

## 目录

1. [按钮组件](#按钮组件)
2. [卡片组件](#卡片组件)
3. [表单组件](#表单组件)
4. [导航组件](#导航组件)
5. [模态框组件](#模态框组件)
6. [通知组件](#通知组件)
7. [图表组件](#图表组件)
8. [响应式工具](#响应式工具)

## 按钮组件

### 基础按钮

```html
<!-- 主要按钮 -->
<button class="btn btn-primary">
    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    主要操作
</button>

<!-- 次要按钮 -->
<button class="btn btn-secondary">次要操作</button>

<!-- 成功按钮 -->
<button class="btn btn-success">成功操作</button>

<!-- 警告按钮 -->
<button class="btn btn-warning">警告操作</button>

<!-- 危险按钮 -->
<button class="btn btn-danger">危险操作</button>
```

### 按钮尺寸

```html
<!-- 小尺寸 -->
<button class="btn btn-primary btn-sm">小按钮</button>

<!-- 默认尺寸 -->
<button class="btn btn-primary">默认按钮</button>

<!-- 大尺寸 -->
<button class="btn btn-primary btn-lg">大按钮</button>
```

### 加载状态

```html
<button class="btn btn-primary" disabled>
    <div class="loading-spinner mr-2"></div>
    加载中...
</button>
```

## 卡片组件

### 基础卡片

```html
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-semibold">卡片标题</h3>
    </div>
    <div class="card-body">
        <p>卡片内容区域</p>
    </div>
    <div class="card-footer">
        <button class="btn btn-primary">操作按钮</button>
    </div>
</div>
```

### 悬停效果卡片

```html
<div class="card card-hover">
    <div class="card-body">
        <h3>悬停时有动画效果</h3>
        <p>鼠标悬停时卡片会轻微上移并增强阴影</p>
    </div>
</div>
```

### 项目报告卡片

```html
<div class="card card-hover group">
    <!-- 项目头图 -->
    <div class="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white">
                <svg class="w-16 h-16 mx-auto mb-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <h3 class="text-lg font-bold">项目名称</h3>
            </div>
        </div>
        <!-- 状态徽章 -->
        <div class="absolute top-4 right-4">
            <span class="badge badge-success">已发布</span>
        </div>
    </div>
    
    <div class="card-body">
        <p class="text-gray-600 dark:text-gray-400 text-sm">项目描述...</p>
        
        <!-- 元信息 -->
        <div class="space-y-2 mt-4">
            <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span>创建者名称</span>
            </div>
        </div>
    </div>
    
    <div class="card-footer">
        <div class="flex gap-2">
            <a href="#" class="btn btn-primary flex-1">分析页面</a>
            <a href="#" class="btn btn-secondary flex-1">研究报告</a>
        </div>
    </div>
</div>
```

## 表单组件

### 基础表单

```html
<form class="space-y-4">
    <div>
        <label class="form-label">标签文本</label>
        <input type="text" class="form-input" placeholder="请输入...">
    </div>
    
    <div>
        <label class="form-label">必填字段 <span class="text-red-500">*</span></label>
        <input type="email" class="form-input" required>
    </div>
    
    <button type="submit" class="btn btn-primary">提交</button>
</form>
```

### 浮动标签表单

```html
<div class="floating-label">
    <input type="email" id="email" class="form-input placeholder-transparent" placeholder="邮箱地址">
    <label for="email" class="text-gray-600">邮箱地址</label>
</div>
```

### 错误状态

```html
<div>
    <label class="form-label">邮箱地址</label>
    <input type="email" class="form-input border-red-500" value="invalid-email">
    <p class="form-error">请输入有效的邮箱地址</p>
</div>
```

## 导航组件

### 主导航栏

```html
<nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="#" class="flex items-center space-x-2 text-xl font-bold text-gradient">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>平台名称</span>
                </a>
            </div>
            
            <!-- 导航链接 -->
            <div class="hidden md:flex items-center space-x-4">
                <a href="#" class="nav-link">首页</a>
                <a href="#" class="nav-link nav-link-active">当前页面</a>
            </div>
        </div>
    </div>
</nav>
```

### 侧边导航

```html
<nav class="w-64 bg-white dark:bg-gray-800 shadow-sm">
    <div class="p-4">
        <ul class="space-y-2">
            <li>
                <a href="#" class="nav-link nav-link-active">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    仪表板
                </a>
            </li>
            <li>
                <a href="#" class="nav-link">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    报告管理
                </a>
            </li>
        </ul>
    </div>
</nav>
```

## 模态框组件

### 基础模态框

```html
<div id="modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4">
        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 modal-backdrop"></div>
        
        <!-- 模态框内容 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">模态框标题</h3>
                <button data-modal-close class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="mb-6">
                <p>模态框内容</p>
            </div>
            
            <div class="flex justify-end space-x-2">
                <button data-modal-close class="btn btn-secondary">取消</button>
                <button class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>
</div>
```

## 通知组件

### 成功通知

```html
<div class="notification notification-success">
    <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>操作成功！</span>
    </div>
</div>
```

### JavaScript 通知

```javascript
// 显示通知
showNotification('操作成功', 'success');
showNotification('发生错误', 'error');
showNotification('警告信息', 'warning');
showNotification('提示信息', 'info');
```

## 图表组件

### 折线图

```javascript
new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
            label: '数据趋势',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
```

### 饼图

```javascript
new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['类别A', '类别B', '类别C'],
        datasets: [{
            data: [30, 50, 20],
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(34, 197, 94)',
                'rgb(251, 191, 36)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
```

## 响应式工具

### 网格系统

```html
<!-- 响应式网格 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div>列1</div>
    <div>列2</div>
    <div>列3</div>
</div>

<!-- 弹性布局 -->
<div class="flex flex-col md:flex-row gap-4">
    <div class="flex-1">主内容</div>
    <div class="w-full md:w-64">侧边栏</div>
</div>
```

### 显示/隐藏工具

```html
<!-- 在小屏幕隐藏 -->
<div class="hidden md:block">桌面端显示</div>

<!-- 在大屏幕隐藏 -->
<div class="block md:hidden">移动端显示</div>
```

## 最佳实践

### 1. 无障碍性
- 使用语义化HTML标签
- 提供适当的ARIA标签
- 确保键盘导航功能
- 保持足够的颜色对比度

### 2. 性能优化
- 使用CSS类而不是内联样式
- 避免不必要的DOM操作
- 合理使用动画效果
- 优化图片和资源加载

### 3. 用户体验
- 提供清晰的视觉反馈
- 保持一致的交互模式
- 合理的加载状态提示
- 友好的错误处理

### 4. 维护性
- 遵循组件化开发
- 保持代码结构清晰
- 添加适当的注释
- 定期更新和优化
