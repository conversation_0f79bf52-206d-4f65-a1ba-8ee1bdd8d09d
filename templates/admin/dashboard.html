{% extends "admin/base.html" %}

{% block title %}仪表板 - 管理后台{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="mb-12">
    <div class="relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-700 to-pink-600 rounded-3xl"></div>
        <div class="absolute inset-0 bg-black/10 rounded-3xl"></div>

        <!-- 装饰性元素 -->
        <div class="absolute top-4 right-4 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
        <div class="absolute bottom-4 left-4 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>

        <div class="relative p-8 md:p-12 text-white">
            <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mr-4 backdrop-blur-sm">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl md:text-4xl font-bold mb-2 text-shadow-lg">
                                欢迎回来，{{ current_user.name }}！
                            </h1>
                            <p class="text-white/90 text-lg">
                                管理您的项目研究报告平台
                            </p>
                        </div>
                    </div>

                    <!-- 快速统计 -->
                    <div class="flex flex-wrap gap-6 text-sm">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-white/80">系统运行正常</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-white/80">最后登录: 刚刚</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="#" class="btn btn-secondary bg-white/20 border-white/30 text-white hover:bg-white hover:text-blue-600 backdrop-blur-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建报告
                    </a>
                    <a href="{{ url_for('public.index') }}" class="btn btn-ghost text-white/90 hover:text-white hover:bg-white/10">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        查看前台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 -mt-6 relative z-10">
    <!-- Total Reports Card -->
    <div class="stats-card card card-hover animate-slide-up">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">{{ stats.total_reports or 0 }}</div>
                <div class="stats-label">研究报告总数</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +12%
                    </span>
                    <span class="text-gray-500 ml-1">较上月</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Requests Card -->
    <div class="stats-card card card-hover animate-slide-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">{{ stats.pending_requests or 0 }}</div>
                <div class="stats-label">待处理请求</div>
                <div class="stats-trend">
                    {% if stats.pending_requests and stats.pending_requests > 0 %}
                    <span class="trend-neutral">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        需要处理
                    </span>
                    {% else %}
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        全部处理完成
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Monthly Reports Card -->
    <div class="stats-card card card-hover animate-slide-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">{{ (stats.recent_reports | length) or 0 }}</div>
                <div class="stats-label">本月新增报告</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +{{ (stats.recent_reports | length) or 0 }}
                    </span>
                    <span class="text-gray-500 ml-1">本月</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Monthly Requests Card -->
    <div class="stats-card card card-hover animate-slide-up" style="animation-delay: 0.3s;">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">{{ (stats.recent_requests | length) or 0 }}</div>
                <div class="stats-label">本月新增请求</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +{{ (stats.recent_requests | length) or 0 }}
                    </span>
                    <span class="text-gray-500 ml-1">本月</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Reports Trend Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">报告趋势</h3>
            <div class="flex space-x-2">
                <button class="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-full">7天</button>
                <button class="text-xs px-3 py-1 text-gray-500 hover:bg-gray-100 rounded-full">30天</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="reportsChart"></canvas>
        </div>
    </div>

    <!-- Request Status Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">请求状态分布</h3>
        </div>
        <div class="h-64">
            <canvas id="requestsChart"></canvas>
        </div>
    </div>
</div>

<!-- Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Reports -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                最近的报告
            </h3>
            <a href="{{ url_for('admin.reports') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                查看全部 →
            </a>
        </div>
        <div class="p-6">
            {% if stats.recent_reports %}
                <div class="space-y-4">
                    {% for report in stats.recent_reports %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">{{ report.project_name }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    创建者: {{ report.creator_name }} •
                                    {{ report.created_at[:10] if report.created_at else '未知时间' }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if report.is_published %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ '已发布' if report.is_published else '草稿' }}
                            </span>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">暂无报告</p>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Recent Requests -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-inbox me-2"></i>最近的请求
                </h5>
                <a href="{{ url_for('admin.requests') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                {% if stats.recent_requests %}
                    <div class="list-group list-group-flush">
                        {% for request in stats.recent_requests %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ request.project_name }}</div>
                                <small class="text-muted">
                                    {{ request.user_email }} | 
                                    {{ moment(request.created_at).fromNow() if request.created_at else '未知时间' }}
                                </small>
                            </div>
                            <span class="badge bg-{{ 'warning' if request.status == 'pending' else 'info' if request.status == 'processing' else 'success' if request.status == 'completed' else 'danger' }} rounded-pill">
                                {% if request.status == 'pending' %}待处理
                                {% elif request.status == 'processing' %}处理中
                                {% elif request.status == 'completed' %}已完成
                                {% elif request.status == 'rejected' %}已拒绝
                                {% else %}{{ request.status }}
                                {% endif %}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">暂无请求</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.create_report') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>添加新报告
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list me-2"></i>管理报告
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.requests') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-inbox me-2"></i>处理请求
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('public.index') }}" target="_blank" class="btn btn-outline-info w-100">
                            <i class="fas fa-external-link-alt me-2"></i>查看网站
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>系统状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-database text-success me-2"></i>
                            <span>数据库连接: <strong class="text-success">正常</strong></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-folder text-success me-2"></i>
                            <span>文件存储: <strong class="text-success">正常</strong></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-warning me-2"></i>
                            <span>邮件服务: <strong class="text-warning">待配置</strong></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化图表
    initializeCharts();

    // 自动刷新统计数据（每5分钟）
    setInterval(function() {
        updateDashboardData();
    }, 5 * 60 * 1000);

    // 添加动画效果
    animateStatCards();
});

// 初始化图表
function initializeCharts() {
    // 报告趋势图表
    const reportsCtx = document.getElementById('reportsChart');
    if (reportsCtx) {
        new Chart(reportsCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '新增报告',
                    data: [2, 1, 3, 2, 4, 1, 2],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 请求状态饼图
    const requestsCtx = document.getElementById('requestsChart');
    if (requestsCtx) {
        new Chart(requestsCtx, {
            type: 'doughnut',
            data: {
                labels: ['待处理', '处理中', '已完成', '已拒绝'],
                datasets: [{
                    data: [{{ stats.pending_requests or 0 }}, 2, 8, 1],
                    backgroundColor: [
                        'rgb(251, 191, 36)',
                        'rgb(59, 130, 246)',
                        'rgb(34, 197, 94)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// 统计卡片动画
function animateStatCards() {
    const cards = document.querySelectorAll('.grid > div');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 更新仪表板数据
function updateDashboardData() {
    fetch('{{ url_for("admin.dashboard") }}?ajax=1')
        .then(response => response.json())
        .then(data => {
            // 更新统计数字
            updateStatNumbers(data);
            showNotification('数据已更新', 'success');
        })
        .catch(error => {
            console.error('更新数据失败:', error);
        });
}

// 更新统计数字
function updateStatNumbers(data) {
    const stats = document.querySelectorAll('.text-3xl');
    stats.forEach(stat => {
        const currentValue = parseInt(stat.textContent);
        const newValue = data[stat.dataset.stat] || currentValue;

        if (currentValue !== newValue) {
            animateNumber(stat, currentValue, newValue);
        }
    });
}

// 数字动画
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.round(start + (end - start) * progress);
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="text-sm font-medium">${message}</span>
            <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>
{% endblock %}
