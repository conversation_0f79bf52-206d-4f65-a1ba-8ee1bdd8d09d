{% extends "base.html" %}

{% block title %}{{ report.project_name }} - 分析页面 - 项目研究报告平台{% endblock %}

{% block extra_css %}
<style>
    .analysis-page {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
    }

    .analysis-hero {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
        position: relative;
        overflow: hidden;
    }

    .analysis-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
    }

    .analysis-hero::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: linear-gradient(to top, rgba(248, 250, 252, 1) 0%, rgba(248, 250, 252, 0) 100%);
    }

    .analysis-content-wrapper {
        position: relative;
        z-index: 10;
        margin-top: -30px;
    }

    .analysis-iframe-container {
        background: white;
        border-radius: 1.5rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .analysis-iframe {
        width: 100%;
        height: 800px;
        border: none;
        display: block;
    }

    .project-info-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(16px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .project-meta-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .meta-item {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 1rem;
        padding: 1.25rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .meta-item:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .meta-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .meta-value {
        font-size: 1.125rem;
        font-weight: 700;
        color: white;
        word-break: break-all;
    }

    .floating-actions {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 50;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .floating-btn {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .floating-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    @media (max-width: 768px) {
        .analysis-iframe {
            height: 600px;
        }

        .floating-actions {
            bottom: 1rem;
            right: 1rem;
        }

        .floating-btn {
            width: 48px;
            height: 48px;
        }

        .project-meta-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="analysis-page">
    <!-- 分析页面Hero区域 -->
    <div class="analysis-hero">
        <div class="relative z-10 px-6 py-16 md:py-24">
            <div class="max-w-7xl mx-auto">
                <!-- 返回按钮 -->
                <div class="mb-8">
                    <a href="{{ url_for('public.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-xl backdrop-blur-sm border border-white/30 hover:bg-white/30 transition-all duration-300 hover:-translate-y-1">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        返回项目列表
                    </a>
                </div>

                <!-- 项目信息卡片 -->
                <div class="project-info-card p-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                        <!-- 项目标题和描述 -->
                        <div class="lg:col-span-2">
                            <div class="flex items-center mb-4">
                                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                                        {{ report.project_name }}
                                    </h1>
                                    <p class="text-lg text-gray-600 dark:text-gray-400">
                                        项目深度分析报告
                                    </p>
                                </div>
                            </div>

                            {% if report.description %}
                            <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                                {{ report.description }}
                            </p>
                            {% endif %}
                        </div>

                        <!-- 项目元信息 -->
                        <div class="project-meta-grid">
                            <div class="meta-item">
                                <div class="meta-label">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    创建者
                                </div>
                                <div class="meta-value">{{ report.creator_name or '未知用户' }}</div>
                            </div>

                            <div class="meta-item">
                                <div class="meta-label">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-1 12a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                                    </svg>
                                    创建时间
                                </div>
                                <div class="meta-value">
                                    {{ report.created_at[:10] if report.created_at else '未知' }}
                                </div>
                            </div>

                            <div class="meta-item">
                                <div class="meta-label">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    最后更新
                                </div>
                                <div class="meta-value">
                                    {{ report.updated_at[:10] if report.updated_at else '未知' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析内容区域 -->
    <div class="analysis-content-wrapper px-6 pb-16">
        <div class="max-w-7xl mx-auto">
            <div class="analysis-iframe-container">
                {% if analysis_content %}
                    <!-- 如果是完整的HTML文档，使用iframe -->
                    {% if analysis_content.startswith('<!DOCTYPE') or analysis_content.startswith('<html') %}
                        <iframe srcdoc="{{ analysis_content | e }}"
                                class="analysis-iframe"
                                title="{{ report.project_name }} 分析页面"
                                sandbox="allow-scripts allow-same-origin allow-forms"
                                loading="lazy">
                        </iframe>
                    {% else %}
                        <!-- 如果是HTML片段，直接嵌入 -->
                        <div class="p-8">
                            {{ analysis_content | safe }}
                        </div>
                    {% endif %}
                {% else %}
                    <!-- 加载状态或错误状态 -->
                    <div class="flex items-center justify-center py-32">
                        <div class="text-center">
                            <div class="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                分析内容暂不可用
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
                                抱歉，该项目的分析页面暂时无法显示。可能是分析正在进行中，或者遇到了技术问题。
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <button onclick="location.reload()"
                                        class="btn btn-primary hover-lift">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    刷新页面
                                </button>
                                <a href="{{ url_for('public.view_report', report_id=report.id) }}"
                                   class="btn btn-secondary hover-lift">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    查看文字报告
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
        <a href="{{ url_for('public.view_report', report_id=report.id) }}"
           class="floating-btn btn-primary"
           title="查看研究报告">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </a>

        {% if report.official_website %}
        <a href="{{ report.official_website }}"
           target="_blank"
           class="floating-btn btn-secondary"
           title="访问官方网站">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
            </svg>
        </a>
        {% endif %}

        <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
                class="floating-btn btn-ghost"
                title="回到顶部">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
            </svg>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 监听iframe加载事件
    $('iframe').on('load', function() {
        console.log('Analysis page loaded successfully');
        
        // 可以在这里添加与iframe内容的交互逻辑
        try {
            const iframe = this;
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            // 添加一些基本的样式调整
            const style = iframeDoc.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 20px;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                
                /* 确保内容适应iframe */
                * {
                    box-sizing: border-box;
                }
            `;
            iframeDoc.head.appendChild(style);
            
        } catch (e) {
            // 跨域限制，无法访问iframe内容
            console.log('Cannot access iframe content due to cross-origin restrictions');
        }
    });
    
    // 处理iframe错误
    $('iframe').on('error', function() {
        console.error('Failed to load analysis page');
        $(this).parent().html(`
            <div class="p-4 text-center">
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    <h3>加载失败</h3>
                    <p class="text-muted">分析页面加载失败，请刷新页面重试。</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>刷新页面
                    </button>
                </div>
            </div>
        `);
    });
    
    // 全屏功能
    $('.btn-fullscreen').on('click', function() {
        const iframe = $('iframe')[0];
        if (iframe.requestFullscreen) {
            iframe.requestFullscreen();
        } else if (iframe.webkitRequestFullscreen) {
            iframe.webkitRequestFullscreen();
        } else if (iframe.msRequestFullscreen) {
            iframe.msRequestFullscreen();
        }
    });
});
</script>
{% endblock %}
