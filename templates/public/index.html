{% extends "base.html" %}

{% block title %}项目研究报告列表 - 项目研究报告平台{% endblock %}
{% block description %}浏览最新的项目研究报告，获取深度技术分析和项目评估{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-700 to-pink-600"></div>
    <div class="absolute inset-0 bg-black/10"></div>

    <!-- 装饰性背景元素 -->
    <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
    <div class="absolute top-32 right-20 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
    <div class="absolute bottom-20 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>

    <div class="relative px-8 py-16 md:py-24">
        <div class="max-w-6xl mx-auto text-center">
            <!-- 主标题 -->
            <div class="mb-8 animate-fade-in">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 text-white text-shadow-lg">
                    项目研究报告
                    <span class="block text-4xl md:text-6xl bg-gradient-to-r from-yellow-300 via-pink-300 to-purple-300 bg-clip-text text-transparent">
                        智能分析平台
                    </span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto leading-relaxed">
                    基于AI驱动的深度技术分析 · 专业项目评估 · 数据驱动决策支持
                </p>
            </div>

            <!-- 特色标签 -->
            <div class="flex flex-wrap justify-center gap-3 mb-10 animate-slide-up">
                <span class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    AI智能分析
                </span>
                <span class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    专业评估
                </span>
                <span class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                    </svg>
                    数据驱动
                </span>
            </div>

            <!-- 行动按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-bounce-in">
                <button type="button" class="btn btn-xl btn-secondary hover-lift" onclick="document.getElementById('search-input').focus()">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    浏览研究报告
                </button>
                <button type="button" class="btn btn-xl btn-primary hover-lift" data-modal-target="requestModal">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    申请新项目分析
                </button>
            </div>
        </div>
    </div>

    <!-- 底部波浪装饰 -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg class="relative block w-full h-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="rgb(249, 250, 251)"></path>
        </svg>
    </div>
</div>

<!-- Stats Section -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 -mt-8 relative z-10">
    <!-- 研究报告统计 -->
    <div class="stats-card card card-hover animate-slide-up">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">{{ reports|length if reports else 0 }}</div>
                <div class="stats-label">研究报告</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +12%
                    </span>
                    <span class="text-gray-500 ml-1">较上月</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 项目分析统计 -->
    <div class="stats-card card card-hover animate-slide-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">100+</div>
                <div class="stats-label">项目分析</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +25%
                    </span>
                    <span class="text-gray-500 ml-1">本周新增</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 用户访问统计 -->
    <div class="stats-card card card-hover animate-slide-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between p-6">
            <div class="flex-1">
                <div class="stats-value">1000+</div>
                <div class="stats-label">用户访问</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +8%
                    </span>
                    <span class="text-gray-500 ml-1">日活跃用户</span>
                </div>
            </div>
            <div class="stats-icon">
                <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="card card-glass mb-12 animate-slide-up" style="animation-delay: 0.3s;">
    <div class="card-body">
        <form method="GET" action="{{ url_for('public.index') }}" class="space-y-6">
            <!-- 搜索标题 -->
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    发现优质项目
                </h2>
                <p class="text-gray-600 dark:text-gray-400">
                    搜索并探索最新的技术项目和深度分析报告
                </p>
            </div>

            <!-- 搜索输入区域 -->
            <div class="search-input max-w-2xl mx-auto">
                <label for="search-input" class="sr-only">搜索项目</label>
                <div class="relative">
                    <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" id="search-input" name="search"
                           class="form-input text-lg"
                           placeholder="搜索项目名称、技术栈或关键词..."
                           value="{{ search_query or '' }}">
                </div>
            </div>

            <!-- 搜索按钮和操作 -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button type="submit" class="btn btn-lg btn-primary hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    搜索项目
                </button>
                {% if search_query %}
                <a href="{{ url_for('public.index') }}" class="btn btn-lg btn-secondary hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    清除搜索
                </a>
                {% endif %}
                <button type="button" class="btn btn-lg btn-ghost hover-lift" data-modal-target="requestModal">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    申请新项目
                </button>
            </div>

            <!-- 搜索结果提示 -->
            {% if search_query %}
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 max-w-2xl mx-auto">
                <div class="flex items-center text-blue-700 dark:text-blue-300">
                    <svg class="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-medium">
                        搜索 "<span class="font-bold">{{ search_query }}</span>" 的结果
                    </span>
                </div>
            </div>
            {% endif %}
        </form>
    </div>
</div>



<!-- Reports Grid -->
{% if reports %}
<div class="mb-8">
    <!-- 网格标题 -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                精选项目报告
            </h2>
            <p class="text-gray-600 dark:text-gray-400">
                探索经过深度分析的优质技术项目
            </p>
        </div>
        <div class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
            </svg>
            <span>{{ reports|length }} 个项目</span>
        </div>
    </div>

    <!-- 项目卡片网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% for report in reports %}
        <div class="project-card card card-hover group animate-fade-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <!-- 项目头部图像 -->
            <div class="card-image relative">
                <div class="project-icon">
                    <div class="text-center">
                        <svg class="w-20 h-20 mx-auto mb-3 opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <h3 class="text-xl font-bold text-shadow">{{ report.project_name }}</h3>
                    </div>
                </div>

                <!-- 状态徽章 -->
                <div class="status-badge">
                    <span class="badge badge-solid badge-success badge-pulse">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        已发布
                    </span>
                </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-body">
                <!-- 项目描述 -->
                <div class="mb-6">
                    {% if report.description %}
                    <p class="text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed">
                        {{ report.description[:150] }}{% if report.description|length > 150 %}...{% endif %}
                    </p>
                    {% else %}
                    <p class="text-gray-500 dark:text-gray-500 italic">暂无项目描述</p>
                    {% endif %}
                </div>

                <!-- 项目元信息 -->
                <div class="space-y-3 mb-6">
                    <!-- 创建者 -->
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">
                            {{ report.creator_name[0].upper() if report.creator_name else 'U' }}
                        </div>
                        <span class="font-medium">{{ report.creator_name or '未知用户' }}</span>
                    </div>

                    <!-- 创建时间 -->
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-1 12a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                        </svg>
                        <span>{{ report.created_at[:10] if report.created_at else '未知时间' }}</span>
                    </div>

                    <!-- 官方网站 -->
                    {% if report.official_website %}
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                        <a href="{{ report.official_website }}" target="_blank"
                           class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 truncate font-medium">
                            {{ report.official_website.replace('https://', '').replace('http://', '') }}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 卡片操作按钮 -->
            <div class="card-footer">
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ url_for('public.view_analysis', report_id=report.id) }}"
                       class="btn btn-primary flex-1 text-center hover-lift group">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        项目分析
                    </a>
                    <a href="{{ url_for('public.view_report', report_id=report.id) }}"
                       class="btn btn-secondary flex-1 text-center hover-lift group">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        研究报告
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Pagination -->
{% if pagination and pagination.total_pages > 1 %}
<nav aria-label="报告分页" class="mt-8">
    <div class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if pagination.has_prev %}
            <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </a>
            {% endif %}
            {% if pagination.has_next %}
            <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </a>
            {% endif %}
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    显示第 <span class="font-medium">{{ (pagination.page - 1) * 10 + 1 }}</span> 到
                    <span class="font-medium">{{ pagination.page * 10 if pagination.page * 10 < pagination.total else pagination.total }}</span> 项，
                    共 <span class="font-medium">{{ pagination.total }}</span> 项结果
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if pagination.has_prev %}
                    <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}

                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ page_num }}
                        </span>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <a href="{{ url_for('public.index', page=page_num, search=search_query) }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ page_num }}
                        </a>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
</nav>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-16">
    <div class="mx-auto max-w-md">
        {% if search_query %}
        <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">没有找到匹配的项目</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
            尝试使用不同的关键词搜索，或者申请添加新项目
        </p>
        {% else %}
        <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无研究报告</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
            成为第一个申请项目研究的用户
        </p>
        {% endif %}

        <button type="button" class="btn btn-primary" data-modal-target="requestModal">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            申请新项目
        </button>
    </div>
</div>
{% endif %}



<!-- Request Project Modal -->
<div id="requestModal" class="modal-container hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- 背景遮罩 -->
    <div class="modal-backdrop" aria-hidden="true"></div>

    <!-- 模态框面板 -->
    <div class="modal-panel animate-scale-in max-w-2xl">
        <!-- 模态框头部 -->
        <div class="modal-header">
            <div class="flex items-center">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="modal-title" id="modal-title">申请项目研究分析</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        提交您感兴趣的项目，我们将为您生成专业的分析报告
                    </p>
                </div>
            </div>
        </div>

        <!-- 模态框内容 -->
        <div class="modal-body">
            <!-- 信息提示 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-5 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-300 mb-2">
                            申请须知
                        </h4>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• 我们使用AI技术对项目进行深度分析</li>
                            <li>• 报告生成通常需要1-3个工作日</li>
                            <li>• 完成后将通过邮件通知您</li>
                            <li>• 请确保提供的项目信息准确有效</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 申请表单 -->
            <form id="requestForm" class="space-y-6">
                <!-- 邮箱地址 -->
                <div class="form-group">
                    <label for="userEmail" class="form-label required">邮箱地址</label>
                    <div class="form-input-icon">
                        <input type="email" id="userEmail" name="email" class="form-input" required
                               placeholder="请输入您的邮箱地址">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                    </div>
                    <p class="form-help">我们将通过此邮箱通知您报告完成情况</p>
                </div>

                <!-- 项目名称 -->
                <div class="form-group">
                    <label for="projectName" class="form-label required">项目名称</label>
                    <div class="form-input-icon">
                        <input type="text" id="projectName" name="project_name" class="form-input" required
                               placeholder="例如：React.js、Vue.js、Angular">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>

                <!-- 官方网站 -->
                <div class="form-group">
                    <label for="officialWebsite" class="form-label required">官方网站</label>
                    <div class="form-input-icon">
                        <input type="url" id="officialWebsite" name="official_website" class="form-input" required
                               placeholder="https://example.com">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <p class="form-help">请提供项目的官方网站或GitHub地址</p>
                </div>
            </form>
        </div>

        <!-- 模态框底部 -->
        <div class="modal-footer">
            <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                <button type="button" data-modal-close class="btn btn-secondary order-2 sm:order-1">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    取消
                </button>
                <button type="button" id="submitRequest" class="btn btn-primary order-1 sm:order-2 hover-lift">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    提交申请
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 模态框功能
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // 添加动画
        setTimeout(() => {
            modal.querySelector('.modal-backdrop').classList.add('opacity-75');
            modal.querySelector('[role="dialog"]').classList.add('opacity-100', 'translate-y-0');
        }, 10);
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.querySelector('.modal-backdrop').classList.remove('opacity-75');
        modal.querySelector('[role="dialog"]').classList.remove('opacity-100', 'translate-y-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }, 300);
    }
}

// 项目申请表单处理
document.getElementById('submitRequest').addEventListener('click', function() {
    const form = document.getElementById('requestForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 显示加载状态
    const submitBtn = this;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading-spinner mr-2"></div>提交中...';
    submitBtn.disabled = true;

    // 基本验证
    if (!data.email || !data.project_name || !data.official_website) {
        showNotification('请填写所有必填字段', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showNotification('请输入有效的邮箱地址', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // URL格式验证
    const urlRegex = /^https?:\/\/.+/;
    if (!urlRegex.test(data.official_website)) {
        showNotification('请输入有效的网站地址（以http://或https://开头）', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 提交请求
    fetch('{{ url_for("public.request_project") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message || '申请提交成功！我们会尽快处理您的请求。', 'success');
            hideModal('requestModal');
            form.reset();
        } else {
            showNotification('错误：' + (result.errors ? result.errors.join(', ') : '提交失败'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('提交时出现错误，请稍后重试', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} animate-slide-down`;

    const iconMap = {
        success: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    };

    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${iconMap[type] || iconMap.info}
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    // 添加到页面
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-20 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// 搜索功能增强
const searchInput = document.getElementById('search-input');
if (searchInput) {
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // 这里可以实现实时搜索建议
                console.log('Searching for:', query);
            }, 300);
        }
    });
}
</script>
{% endblock %}
